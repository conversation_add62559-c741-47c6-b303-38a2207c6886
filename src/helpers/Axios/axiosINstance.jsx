import axios from "axios";
import { clearStorage, fetchFromStorage } from "../context/storage";
import siteConstant from "../constant/siteConstant";

let isRedirecting = false;
const apiInstance = axios.create({
  baseURL: "https://api.flowkar.com/api",
  withCredentials: true,
});

apiInstance.interceptors.request.use((config) => {
  const originalUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.USERDATA
  );
  const switchedUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA
  );

  // Determine which user's token to use
  const currentUser = switchedUserData || originalUserData;
  const clonedConfig = config;

  if (currentUser?.token) {
    clonedConfig.headers = {
      Authorization: `Bearer ${currentUser?.token}`,
      ...clonedConfig.headers,
      "Content-Type":
        clonedConfig.headers["Content-Type"] || "multipart/form-data",
    };

    // Add brand and user headers for user switching
    if (switchedUserData && originalUserData) {
      // When user switching is active, add brand_id and original user_id
      const brandId =
        switchedUserData?.brands?.[0]?.id ||
        switchedUserData?.brand_id ||
        localStorage.getItem("BrandId");

      if (brandId && !clonedConfig.headers.brand) {
        clonedConfig.headers.brand = brandId;
      }

      // Always use original user's ID for authorization when switching
      if (originalUserData.user_id && !clonedConfig.headers.user) {
        clonedConfig.headers.user = originalUserData.user_id;
      }
    }
  } else {
    clonedConfig.headers = {
      "Content-Type": "multipart/form-data",
      ...clonedConfig.headers,
    };
  }
  return clonedConfig;
});

apiInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status } = error.response;
      if (status >= 500 && status <= 599) {
        console.error(`Server Error ${status}: ${error.response.statusText}`);
        window.location.href = "/bad-gateway";
      } else if (status === 403) {
        console.error("Forbidden: Unauthorized access");
        window.location.href = "/PageNotFound";
        clearStorage();
      } else if (status === 400) {
        console.error("Error:", error.response.data.message);
      }
    }
    return Promise.reject(error.response?.data ? error.response.data : error);
  }
);

export default apiInstance;
