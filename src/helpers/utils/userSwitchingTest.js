/**
 * User Switching Test Utility
 * This utility helps test the user switching functionality
 */

import { fetchFromStorage } from "../context/storage";
import siteConstant from "../constant/siteConstant";

/**
 * Test function to verify user switching flow
 */
export const testUserSwitching = () => {
  console.log("=== User Switching Test ===");
  
  // Get current user data
  const originalUserData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
  const switchedUserData = fetchFromStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA);
  
  console.log("Original User Data:", originalUserData);
  console.log("Switched User Data:", switchedUserData);
  
  // Determine current user
  const currentUser = switchedUserData || originalUserData;
  console.log("Current Active User:", currentUser);
  
  // Check localStorage values
  const storedUserId = localStorage.getItem("UserId");
  const storedBrandId = localStorage.getItem("BrandId");
  
  console.log("Stored UserId:", storedUserId);
  console.log("Stored BrandId:", storedBrandId);
  
  // Verify token
  console.log("Current Token:", currentUser?.token ? "Present" : "Missing");
  
  // Check if user switching is active
  const isUsingSwitchedUser = !!switchedUserData;
  console.log("Is Using Switched User:", isUsingSwitchedUser);
  
  if (isUsingSwitchedUser) {
    console.log("✅ User switching is ACTIVE");
    console.log("Switched to:", switchedUserData.name);
    console.log("Original user:", originalUserData.name);
    
    // Verify headers that should be sent
    const expectedBrandId = switchedUserData?.brands?.[0]?.id || 
                           switchedUserData?.brand_id || 
                           localStorage.getItem("BrandId");
    const expectedUserId = originalUserData.user_id;
    
    console.log("Expected API Headers:");
    console.log("- brand:", expectedBrandId);
    console.log("- user:", expectedUserId);
    console.log("- Authorization: Bearer", switchedUserData.token ? "[TOKEN_PRESENT]" : "[TOKEN_MISSING]");
  } else {
    console.log("ℹ️ User switching is NOT active - using original user");
  }
  
  console.log("=== End Test ===");
  
  return {
    originalUserData,
    switchedUserData,
    currentUser,
    isUsingSwitchedUser,
    storedUserId,
    storedBrandId
  };
};

/**
 * Listen for user switching events
 */
export const listenForUserSwitchEvents = () => {
  const handleUserSwitched = (event) => {
    console.log("🔄 User Switch Event Detected:", event.detail);
    
    // Run test after switch
    setTimeout(() => {
      testUserSwitching();
    }, 100);
  };

  window.addEventListener("userSwitched", handleUserSwitched);
  
  console.log("👂 Listening for user switch events...");
  
  // Return cleanup function
  return () => {
    window.removeEventListener("userSwitched", handleUserSwitched);
  };
};

/**
 * Simulate user switch for testing
 */
export const simulateUserSwitch = (targetUser) => {
  console.log("🧪 Simulating user switch to:", targetUser.name);
  
  window.dispatchEvent(
    new CustomEvent("userSwitched", {
      detail: {
        previousUser: fetchFromStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA) || 
                     fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA),
        newUser: targetUser,
        isOriginalUser: false,
        brandId: targetUser.brand_id || targetUser.brands?.[0]?.id,
        timestamp: Date.now(),
      },
    })
  );
};
