# User Switching Implementation

## Overview
This implementation allows users to switch between different user accounts while maintaining proper authentication and data context. When a user switches, the dashboard and all API calls will show the switched user's data while using the original user's credentials for authorization.

## Key Components Modified

### 1. Axios Interceptor (`src/helpers/Axios/axiosINstance.jsx`)
**Changes Made:**
- Modified to detect when user switching is active
- Uses switched user's token for authentication
- Automatically adds `brand` and `user` headers when switching is active
- `brand` header contains the switched user's brand ID
- `user` header contains the original user's ID for authorization

**How it works:**
```javascript
// Determines which user's token to use
const currentUser = switchedUserData || originalUserData;

// Adds headers for user switching
if (switchedUserData && originalUserData) {
  clonedConfig.headers.brand = switchedUserBrandId;
  clonedConfig.headers.user = originalUserData.user_id;
}
```

### 2. Dashboard Component (`src/views/components/dashboard/index.jsx`)
**Changes Made:**
- Added event listener for `userSwitched` events
- Automatically refreshes all dashboard data when user switching occurs
- Added test utility integration for development

**Event Handling:**
```javascript
window.addEventListener("userSwitched", handleUserSwitched);
```

### 3. TodaysPlanner Component
**Changes Made:**
- Added event listener to refresh planner data on user switch
- Ensures scheduled posts show for the switched user

## Data Flow

### When User Switches:
1. **User Selection**: User selects a different user from the navbar dropdown
2. **Token Management**: System uses the switched user's token from localStorage
3. **Header Addition**: Axios interceptor automatically adds:
   - `Authorization: Bearer {switched_user_token}`
   - `brand: {switched_user_brand_id}`
   - `user: {original_user_id}`
4. **Event Dispatch**: `userSwitched` event is dispatched
5. **Data Refresh**: Dashboard components refresh their data
6. **UI Update**: Dashboard shows switched user's data

### Storage Structure:
- `userData`: Original logged-in user data (with token)
- `SwitchUserData`: Currently switched user data (with token)
- `UserId`: Current active user ID in localStorage
- `BrandId`: Current active brand ID in localStorage

## API Headers Sent

### Normal Operation (No Switching):
```
Authorization: Bearer {original_user_token}
```

### During User Switching:
```
Authorization: Bearer {switched_user_token}
brand: {switched_user_brand_id}
user: {original_user_id}
```

## Testing

### Development Testing:
A test utility is included (`src/helpers/utils/userSwitchingTest.js`) that:
- Logs current user switching state
- Monitors user switch events
- Verifies token and header configuration
- Only runs in development mode

### Manual Testing Steps:
1. Login as User A
2. Navigate to dashboard - should show User A's data
3. Click on user dropdown in navbar
4. Select a different user (User B)
5. If User B requires login, complete the login flow
6. Dashboard should refresh and show User B's data
7. All API calls should use User B's token with User A's authorization

## Key Features

### ✅ Implemented:
- Automatic token switching
- Header management for API calls
- Dashboard data refresh on user switch
- Event-driven architecture
- Development testing utilities
- Proper error handling and cleanup

### 🔄 Flow Summary:
1. **Switch User** → Uses switched user's token from local storage
2. **API Calls** → Include brand_id (switched user) + user_id (original user)
3. **Dashboard Refresh** → Shows switched user's data
4. **Context Maintained** → Original user remains for authorization

## Files Modified:
- `src/helpers/Axios/axiosINstance.jsx` - Token and header management
- `src/views/components/dashboard/index.jsx` - Dashboard refresh logic
- `src/helpers/utils/userSwitchingTest.js` - Testing utility (new file)

## Usage:
The implementation is automatic. When users switch accounts through the navbar dropdown, all dashboard API calls will automatically use the correct tokens and headers to display the switched user's data while maintaining proper authorization through the original user.
